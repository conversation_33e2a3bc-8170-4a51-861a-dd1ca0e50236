PODS:
  - AppAuth (2.0.0):
    - AppAuth/Core (= 2.0.0)
    - AppAuth/ExternalUserAgent (= 2.0.0)
  - AppAuth/Core (2.0.0)
  - AppAuth/ExternalUserAgent (2.0.0):
    - AppAuth/Core
  - AppCheckCore (11.2.0):
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - Firebase/Auth (12.2.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 12.2.0)
  - Firebase/CoreOnly (12.2.0):
    - FirebaseCore (~> 12.2.0)
  - firebase_auth (6.0.2):
    - Firebase/Auth (= 12.2.0)
    - firebase_core
    - Flutter
  - firebase_core (4.1.0):
    - Firebase/CoreOnly (= 12.2.0)
    - Flutter
  - FirebaseAppCheckInterop (12.2.0)
  - FirebaseAuth (12.2.0):
    - FirebaseAppCheckInterop (~> 12.2.0)
    - FirebaseAuthInterop (~> 12.2.0)
    - FirebaseCore (~> 12.2.0)
    - FirebaseCoreExtension (~> 12.2.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GTMSessionFetcher/Core (< 6.0, >= 3.4)
    - RecaptchaInterop (~> 101.0)
  - FirebaseAuthInterop (12.2.0)
  - FirebaseCore (12.2.0):
    - FirebaseCoreInternal (~> 12.2.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreExtension (12.2.0):
    - FirebaseCore (~> 12.2.0)
  - FirebaseCoreInternal (12.2.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - Flutter (1.0.0)
  - google_sign_in_ios (0.0.1):
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 9.0)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleSignIn (9.0.0):
    - AppAuth (~> 2.0)
    - AppCheckCore (~> 11.0)
    - GTMAppAuth (~> 5.0)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (5.0.0):
    - AppAuth/Core (~> 2.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - PromisesObjC (2.4.0)
  - RecaptchaInterop (101.0.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter

DEPENDENCIES:
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - AppCheckCore
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseAuthInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - PromisesObjC
    - RecaptchaInterop

EXTERNAL SOURCES:
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"

SPEC CHECKSUMS:
  AppAuth: 1c1a8afa7e12f2ec3a294d9882dfa5ab7d3cb063
  AppCheckCore: cc8fd0a3a230ddd401f326489c99990b013f0c4f
  Firebase: 26f6f8d460603af3df970ad505b16b15f5e2e9a1
  firebase_auth: 9270662fd9824ff308da6b16a118f70a7f91fc81
  firebase_core: 3b9065a1037f7f2e360077d2aa5fb6ec9adb3005
  FirebaseAppCheckInterop: a1b2598c64c5a8c42fd6f6a1c3d0938ae4324678
  FirebaseAuth: 059c11702bdb759bb49b6c7ec6ff67abf21f39c4
  FirebaseAuthInterop: 217702acd4cc6baa98ba9d6c054532e0de0b8a16
  FirebaseCore: 311c48a147ad4a0ab7febbaed89e8025c67510cd
  FirebaseCoreExtension: 73af080c22a2f7b44cefa391dc08f7e4ee162cb5
  FirebaseCoreInternal: 56ea29f3dad2894f81b060f706f9d53509b6ed3b
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  google_sign_in_ios: 4bb0e529b167cadc6ac785b6ed943c0a0a4cc1c9
  GoogleSignIn: c7f09cfbc85a1abf69187be091997c317cc33b77
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMAppAuth: 217a876b249c3c585a54fd6f73e6b58c4f5c4238
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RecaptchaInterop: 11e0b637842dfb48308d242afc3f448062325aba
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440

PODFILE CHECKSUM: 53a6aebc29ccee84c41f92f409fc20cd4ca011f1

COCOAPODS: 1.16.2
