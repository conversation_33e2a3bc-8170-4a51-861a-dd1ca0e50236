import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lovey_app/app/modules/start/hello_lovey_page.dart';
import 'package:lovey_app/app/modules/start/start_page.dart';
import '../../modules/chat/chat_page.dart';
import '../../modules/home/<USER>';
import 'app_routes.dart';

/// Configuração central de navegação do aplicativo
class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: AppRoutes.start,
    routes: [
      GoRoute(
        path: AppRoutes.start,
        name: 'start',
        builder: (context, state) => const StartPage(),
      ),
      GoRoute(
        path: AppRoutes.helloLovey,
        name: 'hello_lovey',
        builder: (context, state) => const HelloLoveyPage(),
      ),
      // Rota inicial - Home
      GoRoute(
        path: AppRoutes.home,
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),

      // Rota do Chat
      GoRoute(
        path: AppRoutes.chat,
        name: 'chat',
        builder: (context, state) => const ChatPage(),
      ),
    ],

    // Tratamento de erros de navegação
    errorBuilder:
        (context, state) => Scaffold(
          appBar: AppBar(title: const Text('Página não encontrada')),
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Erro: ${state.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.go(AppRoutes.home),
                  child: const Text('Voltar ao início'),
                ),
              ],
            ),
          ),
        ),
  );

  static GoRouter get router => _router;
}
