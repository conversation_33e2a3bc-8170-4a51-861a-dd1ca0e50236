import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static const String _userIdKey = 'user_id';
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpireAtKey = 'token_expire_at';

  static StorageService? _instance;
  static SharedPreferences? _preferences;

  StorageService._();

  static Future<StorageService> getInstance() async {
    _instance ??= StorageService._();
    _preferences ??= await SharedPreferences.getInstance();
    return _instance!;
  }

  // Salvar user_id
  Future<bool> saveUserId(int userId) async {
    return await _preferences!.setInt(_userIdKey, userId);
  }

  // Obter user_id
  int? getUserId() {
    return _preferences!.getInt(_userIdKey);
  }

  // Verificar se user_id existe
  bool hasUserId() {
    return _preferences!.containsKey(_userIdKey);
  }

  // Remover user_id
  Future<bool> removeUserId() async {
    return await _preferences!.remove(_userIdKey);
  }

  // Métodos para tokens de autenticação

  // Salvar token de autenticação
  Future<bool> saveAuthToken(String token) async {
    return await _preferences!.setString(_tokenKey, token);
  }

  // Obter token de autenticação
  String? getAuthToken() {
    return _preferences!.getString(_tokenKey);
  }

  // Verificar se token existe
  bool hasAuthToken() {
    return _preferences!.containsKey(_tokenKey);
  }

  // Remover token de autenticação
  Future<bool> removeAuthToken() async {
    return await _preferences!.remove(_tokenKey);
  }

  // Salvar refresh token
  Future<bool> saveRefreshToken(String refreshToken) async {
    return await _preferences!.setString(_refreshTokenKey, refreshToken);
  }

  // Obter refresh token
  String? getRefreshToken() {
    return _preferences!.getString(_refreshTokenKey);
  }

  // Verificar se refresh token existe
  bool hasRefreshToken() {
    return _preferences!.containsKey(_refreshTokenKey);
  }

  // Remover refresh token
  Future<bool> removeRefreshToken() async {
    return await _preferences!.remove(_refreshTokenKey);
  }

  // Salvar data de expiração do token
  Future<bool> saveTokenExpireAt(DateTime expireAt) async {
    return await _preferences!.setString(
      _tokenExpireAtKey,
      expireAt.toIso8601String(),
    );
  }

  // Obter data de expiração do token
  DateTime? getTokenExpireAt() {
    final expireAtString = _preferences!.getString(_tokenExpireAtKey);
    return expireAtString != null ? DateTime.parse(expireAtString) : null;
  }

  // Verificar se data de expiração existe
  bool hasTokenExpireAt() {
    return _preferences!.containsKey(_tokenExpireAtKey);
  }

  // Remover data de expiração do token
  Future<bool> removeTokenExpireAt() async {
    return await _preferences!.remove(_tokenExpireAtKey);
  }

  // Verificar se o token está válido (não expirado)
  bool isTokenValid() {
    if (!hasAuthToken() || !hasTokenExpireAt()) {
      return false;
    }

    final expireAt = getTokenExpireAt();
    if (expireAt == null) {
      return false;
    }

    return DateTime.now().isBefore(expireAt);
  }

  // Limpar todos os dados de autenticação
  Future<void> clearAuthData() async {
    await removeAuthToken();
    await removeRefreshToken();
    await removeTokenExpireAt();
  }

  // Limpar todos os dados
  Future<bool> clearAll() async {
    return await _preferences!.clear();
  }
}
