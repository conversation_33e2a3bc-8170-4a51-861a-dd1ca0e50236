import 'package:lovey_app/app/core/services/storage_service.dart';
import 'package:lovey_app/app/modules/user/services/user_service.dart';
import 'package:lovey_app/app/modules/user/dtos/user.dto.dart';
import 'package:lovey_app/app/modules/user/entities/user.entity.dart';

class AppInitializationService {
  final StorageService _storageService;
  final UserService _userService;

  AppInitializationService(this._storageService, this._userService);

  /// Inicializa o app verificando se o usuário existe ou criando um novo
  Future<int> initializeApp() async {
    // Verifica se já existe um user_id salvo
    if (_storageService.hasUserId()) {
      final userId = _storageService.getUserId();
      if (userId != null) {
        return userId;
      }
    }

    // Se não existe, cria um novo usuário
    final newUser = await _createNewUser();

    // Salva o user_id no storage
    await _storageService.saveUserId(newUser.id!);

    return newUser.id!;
  }

  /// Cria um novo usuário com dados nulos
  Future<UserEntity> _createNewUser() async {
    final userDto = UserDto(name: null, googleId: null, appleId: null);

    final userEntity = await _userService.createUser(userDto);
    return userEntity;
  }

  /// Obtém o user_id atual (se existir)
  int? getCurrentUserId() {
    return _storageService.getUserId();
  }

  /// Remove o usuário atual (para logout/reset)
  Future<void> clearCurrentUser() async {
    await _storageService.removeUserId();
  }
}
