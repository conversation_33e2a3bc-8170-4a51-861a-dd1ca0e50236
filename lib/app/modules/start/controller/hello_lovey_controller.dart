import 'package:lovey_app/app/modules/auth/controllers/auth_controller.dart';
import 'package:lovey_app/app/core/services/storage_service.dart';

class HelloLoveyController {
  late final AuthController _authController;

  HelloLoveyController() {
    _initializeController();
  }

  void _initializeController() async {
    final storageService = await StorageService.getInstance();
    _authController = AuthController(storageService);
  }

  launchPageOfTermsOfUse() {}

  launchPageOfPrivacyPolicy() {}

  Future<bool> handleGoogleSignIn() async {
    return await _authController.handleGoogleSignIn();
  }

  Future<bool> handleAppleSignIn() async {
    return await _authController.handleAppleSignIn();
  }

  Future<bool> handleSignInWithoutAccount() async {
    return await _authController.handleSignInWithoutAccount();
  }
}
