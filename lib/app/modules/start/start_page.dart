import 'package:flutter/material.dart';
import 'package:lovey_app/app/core/navigation/app_routes.dart';
import 'package:lovey_app/app/core/navigation/navigation_service.dart';
import 'package:lovey_app/app/core/util/assets_strings.dart';
import 'package:lovey_app/app/core/services/storage_service.dart';
import 'package:lovey_app/app/widget/background_container.dart';
import 'package:lovey_app/app/widget/custom_image.dart';

class StartPage extends StatefulWidget {
  const StartPage({super.key});

  @override
  State<StartPage> createState() => _StartPageState();
}

class _StartPageState extends State<StartPage> with TickerProviderStateMixin {
  late AnimationController _heartAnimationController;
  late Animation<double> _heartScaleAnimation;
  late Animation<double> _heartOpacityAnimation;

  bool _showSmallHeart = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startHeartPulseAnimation();
    _checkUserAndNavigate();
  }

  void _initializeAnimations() {
    // Controller para a animação do coração pulsando
    _heartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000), // 1 segundo por pulso
      vsync: this,
    );

    // Animação de escala (pulsação)
    _heartScaleAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(
        parent: _heartAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Animação de opacidade para transição suave entre imagens
    _heartOpacityAnimation = Tween<double>(begin: 1.0, end: 0.7).animate(
      CurvedAnimation(
        parent: _heartAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  void _startHeartPulseAnimation() {
    // Inicia a animação de pulsação contínua
    _heartAnimationController.repeat(reverse: true);

    // Alterna entre as duas imagens a cada 800ms
    _alternateHeartImages();
  }

  void _alternateHeartImages() {
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showSmallHeart = !_showSmallHeart;
        });
        _alternateHeartImages(); // Continua alternando
      }
    });
  }

  Future<void> _checkUserAndNavigate() async {
    // Aguarda no mínimo 5 segundos
    await Future.delayed(const Duration(seconds: 5));

    if (!mounted) return;

    try {
      // Verifica se há usuário salvo no storage
      final storageService = await StorageService.getInstance();
      final hasUser = storageService.hasUserId();

      if (hasUser) {
        // Se há usuário, vai para o chat
        NavigationService.goToChat();
      } else {
        // Se não há usuário, vai para hello_lovey
        NavigationService.goTo(AppRoutes.helloLovey);
      }
    } catch (e) {
      // Em caso de erro, vai para hello_lovey por segurança
      NavigationService.goTo(AppRoutes.helloLovey);
    }
  }

  @override
  void dispose() {
    _heartAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundContainer(
      child: Center(
        child: AnimatedBuilder(
          animation: _heartAnimationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _heartScaleAnimation.value,
              child: Opacity(
                opacity: _heartOpacityAnimation.value,
                child: CustomImage(
                  _showSmallHeart
                      ? Assets.svg.heartWithShadowSmall
                      : Assets.svg.heartWithShadowNormal,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
