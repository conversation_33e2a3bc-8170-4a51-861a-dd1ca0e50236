import 'package:lovey_app/app/modules/auth/services/firebase_auth_service.dart';
import 'package:lovey_app/app/modules/user/services/user_service.dart';
import 'package:lovey_app/app/modules/user/dtos/user.dto.dart';
import 'package:lovey_app/app/modules/auth/entities/auth_response.entity.dart';
import 'package:lovey_app/app/core/services/storage_service.dart';
import 'package:lovey_app/app/core/navigation/navigation_service.dart';

class AuthController {
  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();
  final UserService _userService = UserService();
  final StorageService _storageService;

  AuthController(this._storageService);

  /// Login com Google
  Future<bool> handleGoogleSignIn() async {
    try {
      // Fazer login com Google via Firebase
      final userCredential = await _firebaseAuthService.signInWithGoogle();

      if (userCredential == null) {
        // Usuário cancelou o login
        return false;
      }

      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        throw Exception('Erro ao obter dados do usuário do Firebase');
      }

      // Criar ou atualizar usuário no backend
      final userDto = UserDto(
        name: firebaseUser.displayName,
        googleId: firebaseUser.uid,
        appleId: null,
      );

      final authResponse = await _userService.createUser(userDto);

      // Salvar dados de autenticação
      await _saveAuthData(authResponse);

      // Navegar para home
      NavigationService.goToHome();

      return true;
    } catch (e) {
      print('Erro no login com Google: $e');
      return false;
    }
  }

  /// Login com Apple
  Future<bool> handleAppleSignIn() async {
    try {
      // Fazer login com Apple via Firebase
      final userCredential = await _firebaseAuthService.signInWithApple();

      if (userCredential == null) {
        // Usuário cancelou o login
        return false;
      }

      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        throw Exception('Erro ao obter dados do usuário do Firebase');
      }

      // Criar ou atualizar usuário no backend
      final userDto = UserDto(
        name: firebaseUser.displayName,
        googleId: null,
        appleId: firebaseUser.uid,
      );

      final authResponse = await _userService.createUser(userDto);

      // Salvar dados de autenticação
      await _saveAuthData(authResponse);
      print('Glória a Deus!!!\\o/==>${userDto.toString()}');

      // Navegar para home
      NavigationService.goToHome();

      return true;
    } catch (e) {
      print('Erro no login com Apple: $e');
      return false;
    }
  }

  /// Sign in without account (usuário anônimo)
  Future<bool> handleSignInWithoutAccount() async {
    try {
      // Criar usuário anônimo no backend
      final userDto = UserDto(name: null, googleId: null, appleId: null);

      final authResponse = await _userService.createUser(userDto);

      // Salvar dados de autenticação
      await _saveAuthData(authResponse);

      // Navegar para home
      NavigationService.goToHome();

      return true;
    } catch (e) {
      print('Erro no sign in without account: $e');
      return false;
    }
  }

  /// Fazer logout
  Future<void> signOut() async {
    try {
      // Logout do Firebase
      await _firebaseAuthService.signOut();

      // Limpar dados locais
      await _storageService.clearAuthData();
      await _storageService.removeUserId();

      // Navegar para tela inicial
      NavigationService.goTo('/hello_lovey');
    } catch (e) {
      print('Erro no logout: $e');
      rethrow;
    }
  }

  /// Verificar se o usuário está autenticado
  bool isAuthenticated() {
    return _storageService.hasUserId() && _storageService.isTokenValid();
  }

  /// Salvar dados de autenticação no storage
  Future<void> _saveAuthData(AuthResponseEntity authResponse) async {
    await _storageService.saveUserId(authResponse.user.id!);
    await _storageService.saveAuthToken(authResponse.token);
    await _storageService.saveRefreshToken(authResponse.refreshToken);
    await _storageService.saveTokenExpireAt(authResponse.expireAt);
  }
}
