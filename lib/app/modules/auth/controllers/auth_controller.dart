import 'package:lovey_app/app/modules/auth/services/firebase_auth_service.dart';
import 'package:lovey_app/app/modules/user/services/user_service.dart';
import 'package:lovey_app/app/modules/user/dtos/user.dto.dart';
import 'package:lovey_app/app/core/services/storage_service.dart';
import 'package:lovey_app/app/core/navigation/navigation_service.dart';

class AuthController {
  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();
  late final UserService _userService;
  final StorageService _storageService;

  AuthController(this._storageService) {
    _userService = UserService(storageService: _storageService);
  }

  /// Login com Google
  Future<bool> handleGoogleSignIn() async {
    try {
      // Fazer login com Google via Firebase
      final userCredential = await _firebaseAuthService.signInWithGoogle();

      if (userCredential == null) {
        // Usuário cancelou o login
        return false;
      }

      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        throw Exception('Erro ao obter dados do usuário do Firebase');
      }

      // Criar ou atualizar usuário no backend
      final userDto = UserDto(
        name: firebaseUser.displayName,
        googleId: firebaseUser.uid,
        appleId: null,
      );

      final userEntity = await _userService.createUser(userDto);

      // Salvar dados de usuário (apenas o ID, pois não temos tokens de auth neste fluxo)
      await _storageService.saveUserId(userEntity.id!);

      // Navegar para home
      NavigationService.goToHome();

      return true;
    } catch (e) {
      print('Erro no login com Google: $e');
      return false;
    }
  }

  /// Login com Apple
  Future<bool> handleAppleSignIn() async {
    try {
      // Fazer login com Apple via Firebase
      final userCredential = await _firebaseAuthService.signInWithApple();

      if (userCredential == null) {
        // Usuário cancelou o login
        return false;
      }

      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        throw Exception('Erro ao obter dados do usuário do Firebase');
      }

      // Criar ou atualizar usuário no backend
      final userDto = UserDto(
        name: firebaseUser.displayName,
        googleId: null,
        appleId: firebaseUser.uid,
      );

      final userEntity = await _userService.createUser(userDto);

      // Salvar dados de usuário (apenas o ID, pois não temos tokens de auth neste fluxo)
      await _storageService.saveUserId(userEntity.id!);
      print('Glória a Deus!!!\\o/==>${userDto.toString()}');

      // Navegar para home
      NavigationService.goToHome();

      return true;
    } catch (e) {
      print('Erro no login com Apple: $e');
      return false;
    }
  }

  /// Sign in without account (usuário anônimo)
  Future<bool> handleSignInWithoutAccount() async {
    try {
      // Criar usuário anônimo no backend
      final userDto = UserDto(name: null, googleId: null, appleId: null);

      final userEntity = await _userService.createUser(userDto);

      // Salvar dados de usuário (apenas o ID, pois não temos tokens de auth neste fluxo)
      await _storageService.saveUserId(userEntity.id!);

      // Navegar para home
      NavigationService.goToHome();

      return true;
    } catch (e) {
      print('Erro no sign in without account: $e');
      return false;
    }
  }

  /// Fazer logout
  Future<void> signOut() async {
    try {
      // Logout do Firebase
      await _firebaseAuthService.signOut();

      // Limpar dados locais
      await _storageService.clearAuthData();
      await _storageService.removeUserId();

      // Navegar para tela inicial
      NavigationService.goTo('/hello_lovey');
    } catch (e) {
      print('Erro no logout: $e');
      rethrow;
    }
  }

  /// Verificar se o usuário está autenticado
  bool isAuthenticated() {
    return _storageService.hasUserId() && _storageService.isTokenValid();
  }
}
