import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class FirebaseAuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  late final GoogleSignIn _googleSignIn;

  FirebaseAuthService() {
    // Configurar o Google Sign-In com configurações específicas
    _googleSignIn = GoogleSignIn(scopes: ['email', 'profile']);
  }

  /// Login com Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      print('Iniciando login com Google...');

      // Verificar se estamos no simulador (Google Sign-In pode não funcionar no simulador iOS)
      // Por enquanto, vamos tentar o login normalmente

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // O usuário cancelou o login
        print('Login com Google cancelado pelo usuário');
        return null;
      }

      print('Usuário Google obtido: ${googleUser.email}');

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Verificar se temos os tokens necessários
      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Falha ao obter tokens de autenticação do Google');
      }

      print('Tokens obtidos com sucesso');

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Once signed in, return the UserCredential
      final userCredential = await _firebaseAuth.signInWithCredential(
        credential,
      );
      print('Login com Google realizado com sucesso!');
      return userCredential;
    } catch (e) {
      print('Erro no login com Google: $e');

      // Se for um erro de channel, pode ser problema do simulador
      if (e.toString().contains('channel-error')) {
        print(
          'AVISO: Este erro pode ocorrer no simulador iOS. Teste em um dispositivo real.',
        );
      }

      return null;
    }
  }

  /// Login com Apple
  Future<UserCredential?> signInWithApple() async {
    try {
      // Verificar se o Apple Sign-In está disponível
      if (!await SignInWithApple.isAvailable()) {
        print('Apple Sign-In não está disponível neste dispositivo');
        return null;
      }

      // Request credential for the currently signed in Apple account
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create an `OAuthCredential` from the credential returned by Apple
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in the user with Firebase
      return await _firebaseAuth.signInWithCredential(oauthCredential);
    } catch (e) {
      print('Erro no login com Apple: $e');
      // Não fazer rethrow para não quebrar o fluxo
      return null;
    }
  }

  /// Obter o usuário atual
  User? getCurrentUser() {
    return _firebaseAuth.currentUser;
  }

  /// Verificar se o usuário está logado
  bool isUserLoggedIn() {
    return _firebaseAuth.currentUser != null;
  }

  /// Fazer logout
  Future<void> signOut() async {
    try {
      // Fazer logout do Firebase
      await _firebaseAuth.signOut();

      // Fazer logout do Google Sign-In também
      await _googleSignIn.signOut();
    } catch (e) {
      print('Erro no logout: $e');
      rethrow;
    }
  }

  /// Obter o token de ID do usuário atual
  Future<String?> getCurrentUserIdToken() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user != null) {
        return await user.getIdToken();
      }
      return null;
    } catch (e) {
      print('Erro ao obter token: $e');
      return null;
    }
  }

  /// Stream para monitorar mudanças no estado de autenticação
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();
}
