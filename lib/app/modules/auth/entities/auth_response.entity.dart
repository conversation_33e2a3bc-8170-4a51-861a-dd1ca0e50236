import 'package:lovey_app/app/modules/user/entities/user.entity.dart';

class AuthResponseEntity {
  final String token;
  final String refreshToken;
  final UserEntity user;
  final DateTime expireAt;

  AuthResponseEntity({
    required this.token,
    required this.refreshToken,
    required this.user,
    required this.expireAt,
  });

  // Construtor de fábrica para criar um AuthResponseEntity a partir de um Map (ex: JSON da API)
  factory AuthResponseEntity.fromJson(Map<String, dynamic> json) {
    return AuthResponseEntity(
      token: json['token'] as String,
      refreshToken: json['refreshToken'] as String,
      user: UserEntity.fromJson(json['user'] as Map<String, dynamic>),
      expireAt: DateTime.parse(json['expire_at'] as String),
    );
  }

  // Método para converter o objeto AuthResponseEntity de volta para um Map
  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'refreshToken': refreshToken,
      'user': user.toJson(),
      'expire_at': expireAt.toIso8601String(),
    };
  }
}
