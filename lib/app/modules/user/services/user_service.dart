import 'package:lovey_app/app/core/http_client/http_client_implementation.dart';
import 'package:lovey_app/app/modules/user/dtos/user.dto.dart';
import 'package:lovey_app/app/modules/user/entities/user.entity.dart';
import 'package:lovey_app/app/core/services/storage_service.dart';

class UserService {
  final HttpClientImplementation _httpClient = HttpClientImplementation();
  StorageService? _storageService;

  UserService({StorageService? storageService})
    : _storageService = storageService;

  Future<StorageService> get storageService async {
    _storageService ??= await StorageService.getInstance();
    return _storageService!;
  }

  Future<UserEntity> createUser(UserDto user) async {
    final url = 'https://n8n.bsup.com.br/webhook/user';
    final body = user.toJson();

    final response = await _httpClient.post(url, body: body);

    if (response.statusCode != 200) {
      throw Exception('Failed to create user');
    }

    // A API agora retorna apenas os dados do usuário
    final userEntity = UserEntity.fromJson(response.data);

    // Salvar dados do usuário no storage
    final storage = await storageService;
    await storage.saveUserData(userEntity);
    await storage.saveUserId(userEntity.id!);

    return userEntity;
  }

  Future<UserEntity> updateUser(UserEntity user) async {
    final url = 'https://n8n.bsup.com.br/webhook/user';
    final body = user.toJson();

    final response = await _httpClient.put(url, body: body);

    if (response.statusCode != 200) {
      throw Exception('Failed to update user');
    }

    // A API retorna os dados atualizados do usuário
    final updatedUserEntity = UserEntity.fromJson(response.data);

    // Salvar dados atualizados do usuário no storage
    final storage = await storageService;
    await storage.saveUserData(updatedUserEntity);

    return updatedUserEntity;
  }
}
