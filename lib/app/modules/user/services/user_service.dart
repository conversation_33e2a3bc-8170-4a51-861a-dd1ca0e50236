import 'package:lovey_app/app/core/http_client/http_client_implementation.dart';
import 'package:lovey_app/app/modules/user/dtos/user.dto.dart';
import 'package:lovey_app/app/modules/auth/entities/auth_response.entity.dart';

class UserService {
  final HttpClientImplementation _httpClient = HttpClientImplementation();

  Future<AuthResponseEntity> createUser(UserDto user) async {
    final url = 'https://n8n.bsup.com.br/webhook/user';
    final body = user.toJson();

    final response = await _httpClient.post(url, body: body);

    if (response.statusCode != 200) {
      throw Exception('Failed to create user');
    }

    // A API agora retorna token, refreshToken, user e expire_at
    return AuthResponseEntity.fromJson(response.data);
  }
}
