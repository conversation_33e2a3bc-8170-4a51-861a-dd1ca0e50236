import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'app/core/navigation/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inicializar Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Inicializar serviços
  // final storageService = await StorageService.getInstance();
  // final userService = UserService(storageService: storageService);
  // final appInitService = AppInitializationService(storageService, userService);

  // Verificar/criar usuário
  // await appInitService.initializeApp();

  runApp(const LoveyApp());
}

class LoveyApp extends StatelessWidget {
  const LoveyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp.router(
          title: 'Love<PERSON>',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
            useMaterial3: true,
            // Configuração das fontes personalizadas
            fontFamily: 'AlbertSans',
          ),
          routerConfig: AppRouter.router,
        );
      },
    );
  }
}
