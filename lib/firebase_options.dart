// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBD63OMlS3EplPZBZRXb_ewP78u8dubTcU',
    appId: '1:413676197641:android:850c9345ff27322ac3e5d8',
    messagingSenderId: '413676197641',
    projectId: 'quycky-22841',
    databaseURL: 'https://quycky-22841-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'quycky-22841.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCVXG_RJCQ1zofZCKS5gveLrqOtaUBMAA4',
    appId: '1:413676197641:ios:e5db297036c4ae65c3e5d8',
    messagingSenderId: '413676197641',
    projectId: 'quycky-22841',
    databaseURL: 'https://quycky-22841-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'quycky-22841.firebasestorage.app',
    androidClientId: '413676197641-0qtqmm8thmiidk9b9qq5kg0p8vi1lft1.apps.googleusercontent.com',
    iosClientId: '413676197641-ahb30cgnocn1p357gde431da7phhjrcv.apps.googleusercontent.com',
    iosBundleId: 'com.pt.quycky.lovey',
  );

}